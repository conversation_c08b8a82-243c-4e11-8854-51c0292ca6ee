import Message from 'tdesign-miniprogram/message';
import { lockApi, walletApi, orderApi, plateApi } from '~/api/index';
import { splitTimeStr, extractParam } from '~/utils/util';
Page({
  data: {
    lockId: '',
    loading: true,
    spotInfo: {
      id: '',
      ruleId: '',
      parkId: '',
      lotId: '',
      lockId: '',
      code: '',
      location: '',
      shareStartTime: '',
      shareEndTime: '',
      shareTime: '',
      price: '',
      capAmount: '99.00', // 保证金默认值
      parkName: '',
      parkAddress: '',
      isCrossDay: {
        code: 'NO',
        desc: '否'
      },
      useStatus: {
        code: 'NO',
        desc: '否'
      }
    },
    selectedPlate: '',
    selectedPlateId: '',
    plateList: [], // 车牌列表，每项包含 id, plateNo, isDefault
    depositPaid: false, // 默认未支付押金
    depositAmount: '0.00', // 默认押金金额
    estimatedHours: 1, // 默认预计时长为1小时
    estimatedFee: 0, // 初始预计费用，将在calculateFee中计算
    agreementChecked: false,
    showAgreementDialog: false,
    agreementTitle: '',
    agreementContent: '',
    canOrder: false,
    showPlateSelector: false, // 车牌选择器弹窗
    availableHours: [], // 可选的停车时长数组
    currentHourIndex: 0 // 当前选中的时长在数组中的索引
  },

  onLoad(options) {
    const op = decodeURIComponent(options.q) // 获取到二维码原始链接内容
    const { id } = options; // 获取跳转携带的参数
    const finalLockId = extractParam(op)['lockId'] || id
    console.log('decodeURIComponent: ', op, id, finalLockId);
    if (finalLockId) {
      this.setData({ finalLockId });
      this.fetchSpotInfo(finalLockId)
    } else {
      this.showMessage('车位ID不存在', 'error');
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }

    this.fetchUserPlates();
    this.checkDepositStatus();
  },

  onShow() {
    // 页面显示时更新状态
    this.updateOrderButtonStatus();
  },

  // 获取车位详情
  fetchSpotInfo(lockId) {
    this.setData({ loading: true });

    // 使用apiGetLockRuleDetail接口获取车位锁详情
    lockApi.apiGetLockRuleDetail({ lockId })
      .then(res => {
        if (res && res.code === 0 && res.data) {
          const ruleData = res.data;

          // 格式化共享时间
          //const shareTime = `${ruleData.shareStartTime || '00:00'} - ${ruleData.shareEndTime || '00:00'}`;
          const shareTime = splitTimeStr(
            ruleData.shareStartTime,
            ruleData.shareEndTime,
            ruleData.isCrossDay ? (ruleData.isCrossDay.code == 'YES') : false
          )
          // 更新车位信息
          this.setData({
            spotInfo: {
              id: ruleData.id,
              ruleId: ruleData.id,
              parkId: ruleData.parkId,
              lotId: ruleData.lotId,
              lockId: ruleData.lockId,
              code: ruleData.code,
              name: `车位 ${ruleData.code || ''}`,
              location: ruleData.location || '',
              shareStartTime: ruleData.shareStartTime || '',
              shareEndTime: ruleData.shareEndTime || '',
              shareTime: shareTime,
              price: ruleData.price || '3',
              capAmount: ruleData.capAmount || '100',
              parkName: ruleData.parkName || '',
              parkAddress: ruleData.parkAddress || '',
              isCrossDay: ruleData.isCrossDay || { code: 'NO', desc: '否' },
              useStatus: ruleData.useStatus || { code: 'NO', desc: '否' }
            },
            loading: false
          });

          // 获取可选时长选项
          this.fetchShareTimeOptions(ruleData.id);

          // 更新费用估算
          this.calculateFee();

          // 更新按钮状态
          this.updateOrderButtonStatus();
        } else {
          this.showMessage('获取车位信息失败', 'error');
          this.setData({ loading: false });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch(error => {
        this.showMessage('车位筹备中,即将开通');
        this.setData({ loading: false });
        // 返回上一页
        setTimeout(() => {
          wx.switchTab({
            url: `/pages/home-park/index`
          });
        }, 1500);
      });
  },

  // 获取可选停车时长选项
  async fetchShareTimeOptions(ruleId) {
    try {
      const res = await lockApi.apiGetShareTimeOptions({ ruleId });

      if (res && res.code === 0 && res.data && Array.isArray(res.data)) {
        const availableHours = res.data;

        // 确保数组不为空，如果为空则使用默认值
        if (availableHours.length === 0) {
          console.warn('可选时长数组为空，使用默认值');
          this.setData({
            availableHours: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            currentHourIndex: 0,
            estimatedHours: 1
          });
        } else {
          // 设置可选时长数组，默认选择第一个
          this.setData({
            availableHours: availableHours,
            currentHourIndex: 0,
            estimatedHours: availableHours[0]
          });
        }

        // 重新计算费用
        this.calculateFee();
      } else {
        console.error('获取可选时长失败:', res);
        // 使用默认时长选项
        this.setData({
          availableHours: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
          currentHourIndex: 0,
          estimatedHours: 1
        });
        this.calculateFee();
      }
    } catch (error) {
      console.error('获取可选时长错误:', error);
      // 使用默认时长选项
      this.setData({
        availableHours: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        currentHourIndex: 0,
        estimatedHours: 1
      });
      this.calculateFee();
    }
  },

  // 获取用户车牌列表
  async fetchUserPlates() {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      const res = await plateApi.apiGetLicensePlateList();

      if (res && res.code === 0 && res.data) {
        // 处理车牌列表数据 - 新的API返回结构中，数据在data.results中
        const plates = res.data.results || [];

        if (Array.isArray(plates)) {
          // 提取车牌号列表
          const plateNumbers = [];
          let defaultPlate = '';

          // 遍历车牌列表，找出默认车牌
          plates.forEach(plate => {
            plateNumbers.push({
              id: plate.id,
              plateNo: plate.plateNo,
              isDefault: plate.isDefault && plate.isDefault.code === 'YES'
            });

            // 如果是默认车牌，记录下来
            if (plate.isDefault && plate.isDefault.code === 'YES') {
              defaultPlate = plate.plateNo;
            }
          });

          this.setData({
            plateList: plateNumbers,
            selectedPlate: defaultPlate || (plateNumbers.length > 0 ? plateNumbers[0].plateNo : '')
          });
        } else {
          console.error('车牌列表数据格式错误:', plates);
          this.showMessage('获取车牌列表失败', 'error');

          // 设置空数据
          this.setData({
            plateList: [],
            selectedPlate: ''
          });
        }
      } else {
        console.error('获取车牌列表失败:', res);
        this.showMessage('获取车牌列表失败', 'error');

        // 设置空数据
        this.setData({
          plateList: [],
          selectedPlate: ''
        });
      }

      wx.hideLoading();
      this.updateOrderButtonStatus();
    } catch (error) {
      console.error('获取车牌列表错误:', error);
      this.showMessage('获取车牌列表失败', 'error');
      wx.hideLoading();

      // 设置空数据
      this.setData({
        plateList: [],
        selectedPlate: ''
      });

      this.updateOrderButtonStatus();
    }
  },

  // 检查保证金状态
  checkDepositStatus() {
    // 使用apiGetDepositAmount接口获取用户押金支付状态
    walletApi.apiGetDepositAmount()
      .then(res => {
        if (res && res.code === 0) {
          // 判断押金状态: data='99.00'为已支付, data='0.00'为未支付
          const depositAmount = res.data || '0.00';
          const depositPaid = depositAmount !== '0.00';

          this.setData({
            depositPaid,
            depositAmount
          });

          console.log('Deposit status:', { depositPaid, depositAmount });
          this.updateOrderButtonStatus();
        } else {
          console.error('获取押金状态失败:', res);
          // 默认设置为未支付
          this.setData({
            depositPaid: false,
            depositAmount: '0.00'
          });
          this.updateOrderButtonStatus();
        }
      })
      .catch(error => {
        console.error('获取押金状态失败:', error);
        // 出错时默认设置为未支付
        this.setData({
          depositPaid: false,
          depositAmount: '0.00'
        });
        this.updateOrderButtonStatus();
      });
  },

  // 计算预估费用
  calculateFee() {
    const { estimatedHours, spotInfo } = this.data;
    const hourlyRate = parseFloat(spotInfo.price) || 0;
    let fee = hourlyRate * estimatedHours;

    // 检查是否有封顶金额
    if (spotInfo.capAmount && parseFloat(spotInfo.capAmount) > 0) {
      const capAmount = parseFloat(spotInfo.capAmount);
      // 如果计算的费用超过封顶金额，则使用封顶金额
      if (fee > capAmount) {
        fee = capAmount;
      }
    }

    // 保留两位小数并转换为数字
    const formattedFee = parseFloat(fee.toFixed(2));

    this.setData({
      estimatedFee: formattedFee
    });

    return formattedFee; // 返回计算结果，方便其他方法使用
  },

  // 增加预估时间
  increaseTime() {
    const { currentHourIndex, availableHours } = this.data;

    if (availableHours.length === 0) {
      this.showMessage('暂无可选时长', 'warning');
      return;
    }

    if (currentHourIndex < availableHours.length - 1) {
      const newIndex = currentHourIndex + 1;
      this.setData({
        currentHourIndex: newIndex,
        estimatedHours: availableHours[newIndex]
      });
      this.calculateFee();
    } else {
      this.showMessage(`最多可选择${availableHours[availableHours.length - 1]}小时`, 'warning');
    }
  },

  // 减少预估时间
  decreaseTime() {
    const { currentHourIndex, availableHours } = this.data;

    if (availableHours.length === 0) {
      this.showMessage('暂无可选时长', 'warning');
      return;
    }

    if (currentHourIndex > 0) {
      const newIndex = currentHourIndex - 1;
      this.setData({
        currentHourIndex: newIndex,
        estimatedHours: availableHours[newIndex]
      });
      this.calculateFee();
    } else {
      this.showMessage(`最少可选择${availableHours[0]}小时`, 'warning');
    }
  },

  // 显示车牌管理组件
  showPlateSelectorFun() {
    console.log('显示车牌选择器');
    this.setData({
      showPlateSelector: true
    });
  },

  // 关闭车牌管理组件
  closePlateSelector() {
    console.log('关闭车牌选择器');
    this.setData({
      showPlateSelector: false
    });
  },

  // 车牌选择事件
  onPlateSelect(e) {
    const plate = e.detail;
    console.log('页面接收到车牌选择事件:', plate);
    console.log('当前selectedPlate:', this.data.selectedPlate);

    this.setData({
      selectedPlate: plate.plateNo,
      selectedPlateId: plate.id
    });

    console.log('更新后selectedPlate:', this.data.selectedPlate);
    this.updateOrderButtonStatus();
  },

  // 车牌删除事件
  onPlateDelete(e) {
    const plate = e.detail;

    // 如果删除的是当前选中的车牌，清空选中状态
    if (this.data.selectedPlate === plate.plateNo) {
      this.setData({
        selectedPlate: '',
        selectedPlateId: ''
      });

      this.updateOrderButtonStatus();
    }
  },



  // 用户协议勾选变化
  onAgreementChange(e) {
    // TDesign checkbox 组件的 change 事件返回的是 e.detail.checked
    console.log('Checkbox change event:', e.detail);

    this.setData({
      agreementChecked: e.detail.checked
    });

    console.log('Agreement checked state:', this.data.agreementChecked);
    this.updateOrderButtonStatus();
  },

  // 显示协议内容
  showAgreement(e) {
    const { type } = e.currentTarget.dataset;
    let title = '';
    let content = '';

    if (type === 'user') {
      title = '用户协议';
      content = '1. 用户在使用本服务前应当阅读并同意本协议。\n\n2. 用户应当提供真实、准确、完整的个人资料，并保证资料的及时更新。\n\n3. 用户应当妥善保管账号密码，因用户原因导致的账号被盗用的，由用户自行承担责任。\n\n4. 用户在使用本服务时应当遵守相关法律法规，不得利用本服务从事违法违规活动。\n\n5. 用户应当按时缴纳停车费用，如有逾期，平台有权收取额外费用。';
    } else if (type === 'manual') {
      title = '用户手册';
      content = '1. 下单流程：选择车位 -> 选择车牌 -> 确认下单 -> 支付保证金 -> 完成预约。\n\n2. 使用说明：预约成功后，可直接前往停车场停车，系统会自动识别车牌。\n\n3. 计费规则：按小时计费，不足1小时按1小时计算。超出共享时间按4倍收费。\n\n4. 保证金说明：保证金用于担保用户按时缴纳停车费用，停车结束后，保证金将自动退还至用户账户。\n\n5. 取消规则：预约成功后，如需取消，请至少提前30分钟操作，否则可能会产生相应费用。';
    }

    this.setData({
      showAgreementDialog: true,
      agreementTitle: title,
      agreementContent: content
    });
  },

  // 关闭协议弹窗
  closeAgreementDialog() {
    // 用户阅读协议后，自动勾选同意
    this.setData({
      showAgreementDialog: false,
      agreementChecked: true
    });

    // 更新按钮状态
    this.updateOrderButtonStatus();
  },

  // 更新按钮状态
  updateOrderButtonStatus() {
    const { selectedPlate, depositPaid, agreementChecked, spotInfo } = this.data;

    // console.log('Update button status:', {
    //   selectedPlate,
    //   depositPaid,
    //   agreementChecked
    // });

    // 检查车位是否可用
    const isSpotAvailable = spotInfo.useStatus && spotInfo.useStatus.code === 'NO';

    // 更新下单按钮状态
    this.setData({
      canOrder: !!selectedPlate && isSpotAvailable && agreementChecked
    });
  },

  // 支付押金
  onPayDeposit() {
    console.log('Pay deposit button clicked');

    // 检查是否同意协议
    if (!this.data.agreementChecked) {
      this.showMessage('请先阅读并同意用户协议', 'warning');
      return;
    }

    // 调用押金支付流程
    this.initiateDepositPaymentFlow();
  },

  // 确认下单
  onOrderConfirm() {
    console.log('Order confirm button clicked');

    // 检查车位是否被占用
    if (this.data.spotInfo.useStatus && this.data.spotInfo.useStatus.code === 'YES') {
      this.showMessage('该车位已被占用，请选择其他车位', 'error');
      return;
    }

    // 检查是否选择了车牌
    if (!this.data.selectedPlate) {
      this.showMessage('请选择车牌', 'warning');
      return;
    }

    // 调用创建订单函数
    this.createOrder();
  },

  // 押金支付流程函数
  async initiateDepositPaymentFlow() {
    try {
      // 显示加载状态
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      const uniId = await walletApi.apiGenerateUniqueId()
      // 调用押金支付API
      const depositData = await walletApi.apiPayDeposit({
        uniqueId: uniId.data,
        payAmount: '99.00', // 固定押金金额为99.00
        payType: 'DEPOSIT_PAY', // DEPOSIT_PAY-押金支付,ORDER_SUPPLEMENT-订单补缴
        payMethod: 'WXPAY_LITE' // 微信小程序支付
      });

      // 关闭加载状态
      wx.hideLoading();

      if (!depositData || !depositData.data || !depositData.data.timeStamp) {
        const errorMsg = depositData && depositData.msg ? depositData.msg : '获取押金支付信息失败';
        this.showMessage(errorMsg, 'error');
        return;
      }

      // 调起微信支付
      wx.requestPayment({
        timeStamp: depositData.data.timeStamp,
        nonceStr: depositData.data.nonceStr,
        package: depositData.data.package,
        signType: depositData.data.signType || 'RSA',
        paySign: depositData.data.paySign,
        success: (res) => {
          // 支付成功
          this.showMessage('押金支付成功', 'success');

          // 更新押金状态
          this.setData({
            depositPaid: true,
            depositAmount: '99.00'
          });

          // 刷新按钮状态
          this.updateOrderButtonStatus();
        },
        fail: (err) => {
          // 支付失败
          console.error('押金支付失败:', err);
          if (err.errMsg.indexOf('cancel') > -1) {
            this.showMessage('押金支付已取消', 'warning');
          } else {
            this.showMessage('押金支付失败，请重试', 'error');
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('押金支付流程错误:', error);
      this.showMessage('押金支付过程中出现错误，请重试', 'error');
    }
  },

  // 创建订单函数
  async createOrder() {
    try {
      const { spotInfo, selectedPlate, estimatedHours, estimatedFee } = this.data;

      // 确保预计费用已计算
      if (estimatedFee <= 0) {
        this.calculateFee();
      }

      // 显示加载状态
      wx.showLoading({
        title: '创建订单中...',
        mask: true
      });

      // 计算预计停车时长（秒）
      const reservationDuration = estimatedHours * 3600; // 小时转换为秒

      // 调用创建订单API
      const orderResult = await orderApi.apiCreateOrder({
        source: 'MINI_APP',
        amount: '99.00', // 使用固定费用
        lockId: spotInfo.lockId,
        plateNo: selectedPlate,
        reservationDuration: reservationDuration
      });

      // 关闭加载状态
      wx.hideLoading();

      if (orderResult && orderResult.code === 0 && orderResult.data) {
        // 创建订单成功
        const orderId = orderResult.data;
        const lockId = spotInfo.lockId
        this.showMessage('下单成功', 'success');

        // 跳转到订单详情页，添加来源标识
        setTimeout(() => {
          wx.navigateTo({
            url: `/pages/order-detail/index?id=${orderId}&lockId=${lockId}&from=parking-spots-detail`
          });
        }, 1000);
      } else {
        // 创建订单失败
        const errorMsg = orderResult && orderResult.msg ? orderResult.msg : '创建订单失败';
        this.showMessage(errorMsg, 'error');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('创建订单错误:', error);
      this.showMessage('创建订单过程中出现错误，请重试', 'error');
    }
  },

  // 取消订单函数
  async cancelOrder(orderId) {
    if (!orderId) {
      this.showMessage('订单ID不存在', 'error');
      return;
    }

    try {
      // 显示加载状态
      wx.showLoading({
        title: '取消订单中...',
        mask: true
      });

      // 调用取消订单API
      const cancelResult = await orderApi.apiCancelOrder({
        id: orderId
      });

      // 关闭加载状态
      wx.hideLoading();

      if (cancelResult && cancelResult.code === 0) {
        // 取消订单成功
        this.showMessage('订单已取消', 'success');

        // 返回上一页或刷新当前页面
        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      } else {
        // 取消订单失败
        const errorMsg = cancelResult && cancelResult.msg ? cancelResult.msg : '取消订单失败';
        this.showMessage(errorMsg, 'error');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('取消订单错误:', error);
      this.showMessage('取消订单过程中出现错误，请重试', 'error');
    }
  },

  // 返回按钮点击事件
  onBackTap() {
    wx.navigateTo({
      url: '/pages/parking-spots/index'
    });
  },

  // 显示消息提示
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
